package gormxx

import (
	"contentmanager/library/shared"
	"errors"
	"fmt"
	"reflect"
	"time"

	"gorm.io/gorm"
)

type ReservationPlugin struct{}

func (p ReservationPlugin) Name() string {
	return "modification_plugin"
}

func (p ReservationPlugin) Initialize(db *gorm.DB) error {
	if err := db.Callback().Create().Before("gorm:create").Register("modification:before_create", p.beforeCreate); err != nil {
		return err
	}

	if err := db.Callback().Update().Before("gorm:update").Register("modification:before_update", p.beforeUpdate); err != nil {
		return err
	}

	return nil
}

func (p ReservationPlugin) beforeCreate(db *gorm.DB) {
	p.modifyStruct(db)
}

func (p ReservationPlugin) beforeUpdate(db *gorm.DB) {
	p.modifyStruct(db)
}

func (p ReservationPlugin) modifyStruct(db *gorm.DB) {
	if db.Statement.Schema != nil {
		_, ok := db.Statement.Context.Value("app_context").(*shared.AppContext)
		if !ok {
			db.AddError(errors.New("app_context not found in context"))
			return
		}

		// Example: Validation that can break the operation
		if p.shouldRejectOperation(db) {
			db.AddError(errors.New("operation rejected by reservation plugin"))
			return // This stops the operation
		}

		// Your global modification logic here
		// Use reflection to modify fields across all models

		// Example: Set a tracking field
		if field := db.Statement.Schema.LookUpField("UpdatedAt"); field != nil {
			if err := field.Set(db.Statement.Context, db.Statement.ReflectValue, time.Now()); err != nil {
				db.AddError(fmt.Errorf("failed to set UpdatedAt: %w", err))
				return
			}
		}
	}
}

// Example validation method that can reject operations
func (p ReservationPlugin) shouldRejectOperation(db *gorm.DB) bool {
	// Example: Check if the model implements a specific interface
	// and validate business rules

	switch db.Statement.ReflectValue.Kind() {
	case reflect.Struct:
		// Example: Check if this is a reservation-related model
		if reservable, ok := db.Statement.ReflectValue.Interface().(interface{ IsReservable() bool }); ok {
			if !reservable.IsReservable() {
				return true // Reject the operation
			}
		}
	case reflect.Slice, reflect.Array:
		// Handle batch operations
		for i := 0; i < db.Statement.ReflectValue.Len(); i++ {
			item := db.Statement.ReflectValue.Index(i)
			if reservable, ok := item.Interface().(interface{ IsReservable() bool }); ok {
				if !reservable.IsReservable() {
					return true // Reject the entire batch
				}
			}
		}
	}

	return false // Allow the operation
}

//// Register the plugin
//db.Use(ReservationPlugin{})
